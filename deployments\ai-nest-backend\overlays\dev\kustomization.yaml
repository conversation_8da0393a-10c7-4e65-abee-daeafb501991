apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-nest-backend-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels


labels:
- pairs:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "291867ff"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-ai-nest-backend
    source.branch: 18-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-nest-backend



- path: init-container-patch.yaml
  target:
    kind: Deployment
    name: ai-nest-backend


namePrefix: ""
nameSuffix: "-dev"
